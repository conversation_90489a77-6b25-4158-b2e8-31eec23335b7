import UpIcon from '@assets/svg/Up';
import DownIcon from '@assets/svg/Down';
import BulbIcon from '@assets/svg/Bulb';

const QuestionButtons = () => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <button className="flex items-center space-x-1 px-4 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200">
          <UpIcon size={16} />
          <span>1.1K</span>
          <span className="text-gray-400">•</span>
          <DownIcon size={16} />
          <span>42</span>
        </button>

        <button className="flex items-center space-x-1 px-4 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200">
          <BulbIcon size={16} />
          <span>1.1K</span>
        </button>
      </div>
    </div>
  );
};

export default QuestionButtons;
