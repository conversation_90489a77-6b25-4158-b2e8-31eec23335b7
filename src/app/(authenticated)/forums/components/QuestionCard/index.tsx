'use client';

import Image from 'next/image';
import { QuestionCardPropsI } from './types';
import QuestionButtons from '../QuestionButtons';
import QuestionImageViewer from '../QuestionImageViewer';
import QuestionAttachments from '../QuestionAttachments';

const QuestionCard = ({ question }: QuestionCardPropsI) => {
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const imageFiles =
    question.media?.filter(file => file.type === 'image') || [];
  const allImages = [
    ...(question.images || []),
    ...imageFiles.map(file => file.url),
  ];
  const nonImageFiles =
    question.media?.filter(file => file.type !== 'image') || [];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-1.5 hover:shadow-md transition-shadow duration-200 relative">
      {question.status === 'solved' && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            ✓ Solved
          </span>
        </div>
      )}

      {question.tags && question.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4 border-gray-100">
          {question.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h2 className="text-lg font-medium text-gray-900 leading-tight mb-3 hover:text-blue-600 cursor-pointer">
            {question.title}
          </h2>
        </div>
      </div>

      {question.content && (
        <div className="mb-4">
          <p className="text-gray-700 leading-relaxed">
            {truncateContent(question.content)}
          </p>
        </div>
      )}

      <QuestionImageViewer allImages={allImages} question={question} />
      <QuestionAttachments nonImageFiles={nonImageFiles} />

      {question.status === 'solved' && question.topAnswer && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center mb-2">
            <span className="text-green-600 font-medium text-sm">
              ✓ Accepted Answer
            </span>
          </div>
          <div className="flex items-start space-x-3">
            <Image
              src={question.topAnswer.author.avatar}
              alt={question.topAnswer.author.name}
              width={32}
              height={32}
              className="rounded-full"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-sm text-gray-900">
                  {question.topAnswer.author.name}
                </span>
                <span className="text-xs text-gray-500">
                  {question.topAnswer.author.title}
                </span>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">
                {question.topAnswer.content}
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className="text-xs text-gray-500">
                  {question.topAnswer.upvotes} upvotes
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(question.topAnswer.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      <QuestionButtons />
    </div>
  );
};

export default QuestionCard;
