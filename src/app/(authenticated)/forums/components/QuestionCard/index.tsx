'use client';

import React from 'react';
import Image from 'next/image';
import { QuestionCardPropsI } from './types';
import QuestionButtons from '../QuestionButtons';

const QuestionCard = ({ question }: QuestionCardPropsI) => {
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-4 hover:shadow-md transition-shadow duration-200">
      {question.tags && question.tags.length > 0 && (
        <div className="flex flex-wrap gap-2  mb-4 border-gray-100">
          {question.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <div className="flex justify-between items-start ">
        <div className="flex-1">
          <h2 className="text-lg font-medium text-gray-900 leading-tight mb-3 hover:text-blue-600 cursor-pointer">
            {question.title}
          </h2>
        </div>
      </div>

      {question.content && (
        <div className="mb-4">
          <p className="text-gray-700 leading-relaxed">
            {truncateContent(question.content)}
          </p>
        </div>
      )}

      {question.images && question.images.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-1 gap-3">
            {question.images.map((image, index) => (
              <div key={index} className="relative rounded-lg overflow-hidden">
                <Image
                  src={image}
                  alt={`Question image ${index + 1}`}
                  width={600}
                  height={300}
                  className="w-full h-auto object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <QuestionButtons />
    </div>
  );
};

export default QuestionCard;
