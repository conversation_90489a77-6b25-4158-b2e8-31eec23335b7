import React from 'react';
import Image from 'next/image';
import { useState } from 'react';
import { QuestionI } from '@/types';

const QuestionImageViewer = ({
  allImages,
  question,
}: {
  allImages: string[];
  question: QuestionI;
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const nextImage = () => {
    if (question.images && question.images.length > 1) {
      setCurrentImageIndex(prev =>
        prev === question.images!.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (question.images && question.images.length > 1) {
      setCurrentImageIndex(prev =>
        prev === 0 ? question.images!.length - 1 : prev - 1
      );
    }
  };

  return (
    allImages.length > 0 && (
      <div className="mb-4">
        <div className="relative">
          {allImages.length === 1 ? (
            <div className="relative rounded-lg overflow-hidden">
              <Image
                src={allImages[0]}
                alt="Question image"
                width={600}
                height={300}
                className="w-full h-auto object-cover"
              />
            </div>
          ) : (
            <div className="relative rounded-lg overflow-hidden">
              <Image
                src={allImages[currentImageIndex]}
                alt={`Question image ${currentImageIndex + 1}`}
                width={600}
                height={300}
                className="w-full h-auto object-cover"
              />

              <button
                onClick={prevImage}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all"
              >
                ←
              </button>

              <button
                onClick={nextImage}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all"
              >
                →
              </button>

              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {allImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all ${
                      index === currentImageIndex
                        ? 'bg-gray-700'
                        : 'bg-gray-300 bg-opacity-50'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  );
};

export default QuestionImageViewer;
