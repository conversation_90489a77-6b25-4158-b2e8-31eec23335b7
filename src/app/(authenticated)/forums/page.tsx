'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from './components/DashboardLayout';
import { MOCK_USER, MOCK_QUESTIONS } from '../../../constants/mockData';
import useQuestions from '../../../hooks/useQuestions';
import { QuestionI } from '../../../types';

export default function DashboardPage() {
  const [initialQuestions, setInitialQuestions] = useState<QuestionI[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const { loadMoreQuestions, hasMore } = useQuestions();

  useEffect(() => {
    const loadInitialData = async () => {
      setIsInitialLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setInitialQuestions(MOCK_QUESTIONS);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadInitialData();
  }, []);

  return (
    <DashboardLayout
      user={MOCK_USER}
      initialQuestions={initialQuestions}
      isLoading={isInitialLoading}
      onLoadMoreQuestions={loadMoreQuestions}
      hasMoreQuestions={hasMore}
    />
  );
}
