'use client';

import AppleIcon from '@assets/svg/apple';
import GoogleIcon from '@assets/svg/google';

const SocialLogin = () => {
  const handleGoogleLogin = () => {
    console.log('Google login clicked');
  };

  const handleAppleLogin = () => {
    console.log('Apple login clicked');
  };

  return (
    <div className="space-y-4">
      <button
        type="button"
        onClick={handleGoogleLogin}
        className="w-full flex justify-center items-center py-1  border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
      >
        <GoogleIcon />
        Continue with Google
      </button>

      <button
        type="button"
        onClick={handleAppleLogin}
        className="w-full flex justify-center items-center py-1 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
      >
        <AppleIcon />
        Sign in with Apple
      </button>
    </div>
  );
};

export default SocialLogin;
