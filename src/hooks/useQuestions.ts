'use client';

import { useState, useCallback } from 'react';
import { QuestionI } from '../types';
import { MOCK_QUESTIONS } from '../constants/mockData';

const useQuestions = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Simulate API call with delay
  const simulateApiCall = (page: number): Promise<QuestionI[]> => {
    return new Promise(resolve => {
      setTimeout(() => {
        // Generate more mock questions for pagination
        const questionsPerPage = 3;
        const startIndex = (page - 1) * questionsPerPage;

        if (startIndex >= 15) {
          // Simulate end of data after 15 questions
          resolve([]);
          return;
        }

        const questionTitles = [
          'How do you handle imposter syndrome in your career?',
          "What's the best way to learn a new programming language?",
          'How can AI improve healthcare accessibility?',
          'What are the ethical implications of social media algorithms?',
          'How do you maintain work-life balance in tech?',
          "What's the future of remote work?",
          'How do you deal with burnout as a developer?',
          'What are the best practices for code reviews?',
          'How do you stay updated with technology trends?',
          "What's the impact of climate change on tech industry?",
          'How do you build a strong professional network?',
          'What are the challenges of leading a remote team?',
          'How do you approach learning new frameworks?',
          "What's the role of mentorship in career growth?",
          'How do you handle difficult conversations at work?',
        ];

        const newQuestions: QuestionI[] = Array.from(
          { length: Math.min(questionsPerPage, 15 - startIndex) },
          (_, index) => {
            const questionIndex = startIndex + index;
            const baseQuestion =
              MOCK_QUESTIONS[questionIndex % MOCK_QUESTIONS.length];

            return {
              ...baseQuestion,
              id: `question-${questionIndex + 1}`,
              title:
                questionTitles[questionIndex] ||
                `${baseQuestion.title} (Question ${questionIndex + 1})`,
              content: `This is a detailed question about ${questionTitles[questionIndex]?.toLowerCase() || 'various topics'}. I'm looking for insights and experiences from the community.`,
              createdAt: new Date(
                Date.now() - questionIndex * 3600000
              ).toISOString(), // 1 hour apart
              answerCount: Math.floor(Math.random() * 50),
              followCount: Math.floor(Math.random() * 10),
              isFollowing: Math.random() > 0.7,
              lastActivity: `Last followed ${Math.floor(Math.random() * 7)}d`,
              tags: ['career', 'technology', 'advice'].slice(
                0,
                Math.floor(Math.random() * 3) + 1
              ),
            };
          }
        );

        resolve(newQuestions);
      }, 800); // Slightly faster for better UX
    });
  };

  const loadMoreQuestions = useCallback(
    async (page: number): Promise<QuestionI[]> => {
      setIsLoading(true);
      try {
        const newQuestions = await simulateApiCall(page);
        if (newQuestions.length === 0) {
          setHasMore(false);
        }
        return newQuestions;
      } catch (error) {
        console.error('Failed to load questions:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const getInitialQuestions = useCallback(async (): Promise<QuestionI[]> => {
    setIsLoading(true);
    try {
      const questions = await simulateApiCall(1);
      return questions;
    } catch (error) {
      console.error('Failed to load initial questions:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    hasMore,
    loadMoreQuestions,
    getInitialQuestions,
  };
};

export default useQuestions;
