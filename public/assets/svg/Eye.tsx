'use client'

import { FC, SVGProps } from 'react'
import { cn } from '@/utils' 

interface EyeIconProps extends SVGProps<SVGSVGElement> {
  size?: number | string 
  color?: string
  className?: string 
}

const EyeIcon: FC<EyeIconProps> = ({
  size = 25, 
  color = 'black',
  className,
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
      {...props}
    >
      <path
        d="M21.2473 12.6905C21.2214 12.6305 20.5868 11.2113 19.1697 9.78203C17.8547 8.45741 15.5956 6.87741 12.2869 6.87741C8.97818 6.87741 6.719 8.45741 5.40407 9.78203C3.98693 11.2113 3.35235 12.6282 3.32642 12.6905C3.30034 12.7496 3.28687 12.8135 3.28687 12.8782C3.28687 12.9428 3.30034 13.0068 3.32642 13.0659C3.35235 13.1251 3.98693 14.5443 5.40407 15.9736C6.719 17.2982 8.97818 18.8774 12.2869 18.8774C15.5956 18.8774 17.8547 17.2982 19.1697 15.9736C20.5868 14.5443 21.2214 13.1274 21.2473 13.0659C21.2734 13.0068 21.2869 12.9428 21.2869 12.8782C21.2869 12.8135 21.2734 12.7496 21.2473 12.6905ZM12.2869 17.9543C9.89345 17.9543 7.80359 17.0759 6.0745 15.3443C5.34967 14.6178 4.73631 13.7864 4.25465 12.8774C4.73617 11.9686 5.34955 11.1374 6.0745 10.4113C7.80359 8.67895 9.89345 7.80049 12.2869 7.80049C14.6803 7.80049 16.7701 8.67895 18.4992 10.4113C19.2242 11.1374 19.8376 11.9686 20.3191 12.8774C19.8332 13.8166 17.3971 17.9543 12.2869 17.9543ZM12.2869 9.33895C11.5929 9.33895 10.9146 9.54648 10.3376 9.93529C9.76066 10.3241 9.31097 10.8767 9.04542 11.5233C8.77987 12.1699 8.71039 12.8813 8.84576 13.5677C8.98114 14.2541 9.31529 14.8846 9.80597 15.3795C10.2966 15.8743 10.9218 16.2113 11.6024 16.3479C12.283 16.4844 12.9884 16.4143 13.6295 16.1465C14.2706 15.8787 14.8186 15.4252 15.2041 14.8433C15.5896 14.2614 15.7954 13.5773 15.7954 12.8774C15.7942 11.9393 15.4241 11.04 14.7664 10.3767C14.1087 9.71336 13.217 9.34017 12.2869 9.33895ZM12.2869 15.4928C11.774 15.4928 11.2726 15.3394 10.8461 15.052C10.4197 14.7646 10.0873 14.3562 9.89101 13.8783C9.69474 13.4004 9.64338 12.8745 9.74344 12.3672C9.8435 11.8598 10.0905 11.3938 10.4532 11.0281C10.8158 10.6623 11.2779 10.4132 11.7809 10.3123C12.284 10.2114 12.8054 10.2632 13.2793 10.4611C13.7531 10.6591 14.1581 10.9943 14.4431 11.4244C14.728 11.8545 14.8801 12.3601 14.8801 12.8774C14.8801 13.5711 14.6069 14.2363 14.1206 14.7268C13.6342 15.2172 12.9746 15.4928 12.2869 15.4928Z"
        fill={color}
      />
    </svg>
  )
}

EyeIcon.displayName = 'EyeIcon'
export  default EyeIcon;